// processImportDiscraftOslMapAndStatusTask.js - Process import_discraft_osl_map_and_status tasks
import { google } from 'googleapis';
import dotenv from 'dotenv';
import ExcelJS from 'exceljs';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

/**
 * Initialize Google Sheets API client with authentication
 * @returns {Object} - Google Sheets API client
 */
async function initializeGoogleSheetsAPI() {
    let auth;

    // Try different authentication methods
    if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY) {
        // Method 1: Service Account Key from environment variable (JSON string)
        console.log('[processImportDiscraftOslMapAndStatusTask] Using service account key from environment variable');
        try {
            const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY);
            auth = new google.auth.GoogleAuth({
                credentials: credentials,
                scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
            });
        } catch (error) {
            throw new Error(`Invalid GOOGLE_SERVICE_ACCOUNT_KEY format: ${error.message}`);
        }
    } else if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        // Method 2: Service Account Key file path
        console.log('[processImportDiscraftOslMapAndStatusTask] Using service account key file from GOOGLE_APPLICATION_CREDENTIALS');
        auth = new google.auth.GoogleAuth({
            keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
            scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
        });
    } else if (process.env.GOOGLE_API_KEY) {
        // Method 3: API Key (for public sheets only, but simpler setup)
        console.log('[processImportDiscraftOslMapAndStatusTask] Using API key authentication');
        auth = process.env.GOOGLE_API_KEY;
    } else {
        throw new Error(`Google Sheets authentication not configured.

Quick setup (choose one):

🔑 API Key (2 minutes):
   1. Get API key from Google Cloud Console
   2. Add GOOGLE_API_KEY=your-key to .env file
   3. Make sheet viewable by anyone with link

🔒 Service Account (5 minutes, more secure):
   1. Create service account in Google Cloud Console
   2. Add GOOGLE_SERVICE_ACCOUNT_KEY={"type":"service_account",...} to .env file
   3. Share sheet with service account email

See QUICK_GOOGLE_SHEETS_SETUP.md for detailed steps.
Test your setup with: node setupGoogleSheetsAPI.js`);
    }

    return google.sheets({ version: 'v4', auth });
}

/**
 * Extract spreadsheet ID from Google Sheets URL
 * @param {string} sheetsUrl - The Google Sheets URL
 * @returns {string} - The spreadsheet ID
 */
function extractSpreadsheetId(sheetsUrl) {
    const match = sheetsUrl.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
    if (!match) {
        throw new Error('Invalid Google Sheets URL format');
    }
    return match[1];
}

/**
 * Fetch data from Google Sheets using the API
 * @param {string} sheetsUrl - The Google Sheets URL
 * @param {string} sheetName - The sheet name
 * @returns {Array} - Array of row arrays
 */
async function fetchGoogleSheetsData(sheetsUrl, sheetName) {
    console.log(`[processImportDiscraftOslMapAndStatusTask] Fetching ${sheetName} sheet data using Google Sheets API...`);

    const spreadsheetId = extractSpreadsheetId(sheetsUrl);
    const sheets = await initializeGoogleSheetsAPI();

    try {
        // Get the sheet data - use a large range to capture all data
        const response = await sheets.spreadsheets.values.get({
            spreadsheetId: spreadsheetId,
            range: `${sheetName}!A1:ZZ1000`, // Get all data from the sheet (up to column ZZ, row 1000)
            valueRenderOption: 'UNFORMATTED_VALUE',
            dateTimeRenderOption: 'FORMATTED_STRING'
        });

        const values = response.data.values || [];
        console.log(`[processImportDiscraftOslMapAndStatusTask] ${sheetName} sheet has ${values.length} rows`);

        return values;
    } catch (error) {
        console.error(`[processImportDiscraftOslMapAndStatusTask] Error fetching ${sheetName} sheet:`, error);

        if (error.code === 403) {
            throw new Error(`Access denied to Google Sheet.

If using API Key:
   • Make sure the sheet is viewable by anyone with the link
   • Go to Share > Change to anyone with the link > Viewer

If using Service Account:
   • Share the sheet with your service account email
   • Give it Viewer permissions
   • Service account email looks like: <EMAIL>

Also check:
   • Google Sheets API is enabled in Google Cloud Console
   • Your credentials are correct

Run 'node setupGoogleSheetsAPI.js' to test your setup.
Original error: ${error.message}`);
        } else if (error.code === 404) {
            throw new Error(`Sheet "${sheetName}" not found. Please check:
1. The sheet name is correct (case-sensitive)
2. The spreadsheet ID is correct
3. The sheet exists in the spreadsheet

Original error: ${error.message}`);
        } else {
            throw new Error(`Failed to fetch ${sheetName} sheet: ${error.message}`);
        }
    }
}

/**
 * Convert column number to letter (1=A, 2=B, etc.)
 * @param {number} colNum - Column number (1-based)
 * @returns {string} - Column letter
 */
function columnNumberToLetter(colNum) {
    let result = '';
    while (colNum > 0) {
        colNum--;
        result = String.fromCharCode(65 + (colNum % 26)) + result;
        colNum = Math.floor(colNum / 26);
    }
    return result;
}

/**
 * Find all cells that start with 'OS' and return their positions and values
 * @param {Array} data - 2D array of sheet data
 * @returns {Array} - Array of objects with row, col, and id
 */
function findOSValues(data) {
    const osValues = [];
    
    for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
        const row = data[rowIndex];
        for (let colIndex = 0; colIndex < row.length; colIndex++) {
            const cellValue = row[colIndex];
            if (cellValue && typeof cellValue === 'string' && cellValue.startsWith('OS')) {
                // Extract the numeric part after 'OS'
                const numericPart = cellValue.substring(2);
                if (numericPart && !isNaN(numericPart)) {
                    osValues.push({
                        row: (rowIndex + 1).toString(), // 1-based row number
                        col: columnNumberToLetter(colIndex + 1), // 1-based column letter
                        id: parseInt(numericPart), // Numeric part as integer
                        originalValue: cellValue
                    });
                }
            }
        }
    }
    
    return osValues;
}

/**
 * Get status values from the 'New' sheet based on row and column positions
 * @param {Array} newSheetData - 2D array of 'New' sheet data
 * @param {Array} osValues - Array of OS values with positions
 * @returns {Array} - Array of OS values with status added
 */
function getStatusValues(newSheetData, osValues) {
    return osValues.map(osValue => {
        const rowIndex = parseInt(osValue.row) - 1; // Convert to 0-based
        const colIndex = osValue.col.charCodeAt(0) - 65; // Convert letter to 0-based index
        
        let status = 'In Stock'; // Default status
        let cellValue = null;
        
        // Check if the position exists in the new sheet data
        if (rowIndex >= 0 && rowIndex < newSheetData.length) {
            const row = newSheetData[rowIndex];
            if (colIndex >= 0 && colIndex < row.length) {
                cellValue = row[colIndex];
                
                // Determine status based on cell value
                if (cellValue === 'N/A') {
                    status = 'Not Available';
                } else if (cellValue === 'out' || cellValue === 'sold out') {
                    status = 'Out of Stock';
                } else if (cellValue === null || cellValue === undefined || cellValue === '') {
                    status = 'In Stock';
                } else {
                    // For any other value, default to In Stock
                    status = 'In Stock';
                }
            }
        }
        
        return {
            ...osValue,
            status: status,
            cellValue: cellValue
        };
    });
}

/**
 * Calculate disc counts for OSL map records
 * @param {Object} supabase - Supabase client
 * @param {Array} oslIds - Array of OSL IDs to calculate counts for
 * @returns {Object} - Results of the calculation
 */
async function calculateDiscCounts(supabase, oslIds) {
    console.log(`[calculateDiscCounts] Calculating disc counts for ${oslIds.length} OSL records...`);

    let updatedRecords = 0;
    let totalInStock = 0;
    let totalSoldLast90 = 0;
    const errors = [];

    // Process in batches to avoid overwhelming the database
    const batchSize = 50;
    for (let i = 0; i < oslIds.length; i += batchSize) {
        const batch = oslIds.slice(i, i + batchSize);
        console.log(`[calculateDiscCounts] Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(oslIds.length / batchSize)} (${batch.length} records)`);

        try {
            // Calculate counts for this batch using a single query
            const { data: countData, error: countError } = await supabase.rpc('calculate_discraft_osl_disc_counts', {
                osl_ids: batch
            });

            if (countError) {
                // If the stored procedure doesn't exist, fall back to individual queries
                if (countError.message.includes('function') || countError.message.includes('schema cache')) {
                    console.log(`[calculateDiscCounts] Stored procedure not found, using individual queries for batch`);
                    const batchResults = await calculateDiscCountsIndividually(supabase, batch);
                    updatedRecords += batchResults.updatedRecords;
                    totalInStock += batchResults.totalInStock;
                    totalSoldLast90 += batchResults.totalSoldLast90;
                    if (batchResults.errors && batchResults.errors.length > 0) {
                        errors.push(...batchResults.errors);
                    }
                } else {
                    throw countError;
                }
            } else {
                // Process the results from the stored procedure
                for (const record of countData) {
                    updatedRecords++;
                    totalInStock += record.in_stock || 0;
                    totalSoldLast90 += record.sold_last_90 || 0;
                }
            }
        } catch (error) {
            console.error(`[calculateDiscCounts] Error processing batch: ${error.message}`);
            errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${error.message}`);
        }
    }

    return {
        updatedRecords,
        totalInStock,
        totalSoldLast90,
        errors: errors.length > 0 ? errors : null
    };
}

/**
 * Calculate disc counts individually (fallback method)
 * @param {Object} supabase - Supabase client
 * @param {Array} oslIds - Array of OSL IDs to calculate counts for
 * @returns {Object} - Results of the calculation
 */
async function calculateDiscCountsIndividually(supabase, oslIds) {
    let updatedRecords = 0;
    let totalInStock = 0;
    let totalSoldLast90 = 0;
    const errors = [];

    for (const oslId of oslIds) {
        try {
            // Count in-stock discs (sold_date is null)
            const { count: inStockCount, error: inStockError } = await supabase
                .from('t_discs')
                .select('*', { count: 'exact', head: true })
                .eq('vendor_osl_id', oslId)
                .is('sold_date', null);

            if (inStockError) {
                throw new Error(`Error counting in-stock discs: ${inStockError.message}`);
            }

            // Count discs sold in last 90 days
            const ninetyDaysAgo = new Date();
            ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

            const { count: soldLast90Count, error: soldError } = await supabase
                .from('t_discs')
                .select('*', { count: 'exact', head: true })
                .eq('vendor_osl_id', oslId)
                .gte('sold_date', ninetyDaysAgo.toISOString());

            if (soldError) {
                throw new Error(`Error counting sold discs: ${soldError.message}`);
            }

            // Update the OSL map record
            const { error: updateError } = await supabase
                .from('it_discraft_osl_map')
                .update({
                    in_stock: inStockCount || 0,
                    sold_last_90: soldLast90Count || 0
                })
                .eq('id', oslId);

            if (updateError) {
                throw new Error(`Error updating OSL record: ${updateError.message}`);
            }

            updatedRecords++;
            totalInStock += inStockCount || 0;
            totalSoldLast90 += soldLast90Count || 0;

        } catch (error) {
            console.error(`[calculateDiscCountsIndividually] Error processing OSL ID ${oslId}: ${error.message}`);
            errors.push(`OSL ID ${oslId}: ${error.message}`);
        }
    }

    return {
        updatedRecords,
        totalInStock,
        totalSoldLast90,
        errors
    };
}

/**
 * Update the Discraft Excel file with qty values from it_discraft_osl_map
 * @param {Object} supabase - Supabase client
 * @returns {Object} - Results of the Excel update
 */
async function updateDiscraftExcelFile(supabase) {
    const excelFilePath = 'C:\\Users\\<USER>\\supabase_project\\data\\external data\\discraftstock.xlsx';

    try {
        // Check if the Excel file exists
        if (!fs.existsSync(excelFilePath)) {
            throw new Error(`Excel file not found: ${excelFilePath}`);
        }

        console.log(`[updateDiscraftExcelFile] Loading Excel file: ${excelFilePath}`);

        // Load the Excel workbook
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(excelFilePath);

        // Get the first worksheet (assuming the data is in the first sheet)
        const worksheet = workbook.worksheets[0];
        if (!worksheet) {
            throw new Error('No worksheets found in the Excel file');
        }

        console.log(`[updateDiscraftExcelFile] Working with worksheet: ${worksheet.name}`);

        // Get all OSL map records with non-zero qty values
        const { data: oslMapData, error: oslError } = await supabase
            .from('it_discraft_osl_map')
            .select('id, row, col, qty')
            .neq('qty', 0)
            .not('qty', 'is', null);

        if (oslError) {
            throw new Error(`Failed to fetch OSL map data: ${oslError.message}`);
        }

        console.log(`[updateDiscraftExcelFile] Found ${oslMapData.length} records with non-zero qty values`);

        let updatedCells = 0;
        const updateLog = [];

        // Update each cell in the Excel file
        for (const record of oslMapData) {
            try {
                const rowNumber = parseInt(record.row);
                const columnLetter = record.col;
                const qtyValue = record.qty;

                // Convert column letter to column number (A=1, B=2, etc.)
                const columnNumber = columnLetter.split('').reduce((result, char) => {
                    return result * 26 + (char.charCodeAt(0) - 'A'.charCodeAt(0) + 1);
                }, 0);

                // Get the cell and update its value
                const cell = worksheet.getCell(rowNumber, columnNumber);
                const oldValue = cell.value;
                cell.value = qtyValue;

                updatedCells++;
                updateLog.push({
                    oslId: record.id,
                    position: `${columnLetter}${rowNumber}`,
                    oldValue: oldValue,
                    newValue: qtyValue
                });

                console.log(`[updateDiscraftExcelFile] Updated cell ${columnLetter}${rowNumber}: ${oldValue} → ${qtyValue} (OSL ID: ${record.id})`);

            } catch (cellError) {
                console.error(`[updateDiscraftExcelFile] Error updating cell for OSL ID ${record.id}: ${cellError.message}`);
            }
        }

        // Create a timestamped filename for the copy
        const now = new Date();
        const timestamp = now.toISOString()
            .replace(/:/g, '-')
            .replace(/\./g, '-')
            .replace('T', '_')
            .substring(0, 19); // Remove milliseconds and timezone

        const directory = path.dirname(excelFilePath);
        const filename = path.basename(excelFilePath, '.xlsx');
        const timestampedFilePath = path.join(directory, `${filename}_${timestamp}.xlsx`);

        // Save the updated workbook with timestamp
        console.log(`[updateDiscraftExcelFile] Saving updated file as: ${timestampedFilePath}`);
        await workbook.xlsx.writeFile(timestampedFilePath);

        // Also save over the original file
        console.log(`[updateDiscraftExcelFile] Updating original file: ${excelFilePath}`);
        await workbook.xlsx.writeFile(excelFilePath);

        return {
            success: true,
            message: `Updated ${updatedCells} cells in Excel file`,
            updatedCells: updatedCells,
            recordsProcessed: oslMapData.length,
            originalFile: excelFilePath,
            timestampedFile: timestampedFilePath,
            updateLog: updateLog.slice(0, 10) // Include first 10 updates for logging
        };

    } catch (error) {
        console.error(`[updateDiscraftExcelFile] Error: ${error.message}`);
        return {
            success: false,
            error: error.message,
            updatedCells: 0,
            recordsProcessed: 0
        };
    }
}

/**
 * Process a import_discraft_osl_map_and_status task
 * @param {Object} task - The task object from the task queue
 * @param {Object} options - Options including supabase client and helper functions
 * @returns {Promise<void>}
 */
export async function processImportDiscraftOslMapAndStatusTask(task, { supabase, updateTaskStatus, logError }) {
    console.log(`[processImportDiscraftOslMapAndStatusTask] Processing task ${task.id} of type ${task.task_type}`);

    const startTime = new Date();

    try {
        // Update task to 'processing' status
        await updateTaskStatus(task.id, 'processing');

        // Parse the payload
        let payload;
        try {
            if (typeof task.payload === 'object' && task.payload !== null) {
                payload = task.payload;
            } else if (typeof task.payload === 'string') {
                payload = JSON.parse(task.payload);
            } else {
                throw new Error(`Unsupported payload type: ${typeof task.payload}`);
            }
        } catch (err) {
            throw new Error(`Failed to parse payload: ${err.message}`);
        }

        console.log(`[processImportDiscraftOslMapAndStatusTask] Parsed payload: ${JSON.stringify(payload)}`);

        // Use the hardcoded Google Sheets URL
        const googleSheetsUrl = 'https://docs.google.com/spreadsheets/d/1x0C8UmI-ukFP0r2rCM9wNipQ53XakFv30wQiYwJYgC8/edit?gid=1907003661#gid=1907003661';

        console.log(`[processImportDiscraftOslMapAndStatusTask] Fetching Map sheet data...`);
        
        // Step 1: Fetch the 'Map' sheet data
        const mapSheetData = await fetchGoogleSheetsData(googleSheetsUrl, 'Map');
        console.log(`[processImportDiscraftOslMapAndStatusTask] Map sheet has ${mapSheetData.length} rows`);

        // Step 2: Find all OS values in the Map sheet
        const osValues = findOSValues(mapSheetData);
        console.log(`[processImportDiscraftOslMapAndStatusTask] Found ${osValues.length} OS values in Map sheet`);

        if (osValues.length === 0) {
            throw new Error('No OS values found in the Map sheet');
        }

        // Step 3: Fetch the 'New' sheet data
        console.log(`[processImportDiscraftOslMapAndStatusTask] Fetching New sheet data...`);
        const newSheetData = await fetchGoogleSheetsData(googleSheetsUrl, 'New');
        console.log(`[processImportDiscraftOslMapAndStatusTask] New sheet has ${newSheetData.length} rows`);

        // Step 4: Get status values from the New sheet
        const osValuesWithStatus = getStatusValues(newSheetData, osValues);
        console.log(`[processImportDiscraftOslMapAndStatusTask] Processed status for ${osValuesWithStatus.length} OS values`);

        // Step 5: Truncate the existing table
        console.log(`[processImportDiscraftOslMapAndStatusTask] Truncating it_discraft_osl_map table...`);
        const { error: deleteError } = await supabase
            .from('it_discraft_osl_map')
            .delete()
            .neq('id', 0); // Delete all records

        if (deleteError) {
            throw new Error(`Failed to truncate table: ${deleteError.message}`);
        }

        // Step 6: Insert the new data
        console.log(`[processImportDiscraftOslMapAndStatusTask] Inserting ${osValuesWithStatus.length} records...`);
        
        const insertData = osValuesWithStatus.map(item => ({
            id: item.id,
            row: item.row,
            col: item.col,
            status: item.status
        }));

        const { error: insertError } = await supabase
            .from('it_discraft_osl_map')
            .insert(insertData);

        if (insertError) {
            throw new Error(`Failed to insert data: ${insertError.message}`);
        }

        // Step 7: Calculate disc counts for each OSL map record
        console.log(`[processImportDiscraftOslMapAndStatusTask] Calculating disc counts for ${osValuesWithStatus.length} OSL records...`);

        const discCountResults = await calculateDiscCounts(supabase, osValuesWithStatus.map(item => item.id));

        console.log(`[processImportDiscraftOslMapAndStatusTask] Updated disc counts for ${discCountResults.updatedRecords} records`);

        // Step 8: Update Excel file with qty values
        console.log(`[processImportDiscraftOslMapAndStatusTask] Updating Excel file with qty values...`);

        const excelUpdateResults = await updateDiscraftExcelFile(supabase);

        console.log(`[processImportDiscraftOslMapAndStatusTask] Excel update completed: ${excelUpdateResults.message}`);

        const endTime = new Date();
        const duration = endTime - startTime;

        const result = {
            success: true,
            message: `Successfully imported ${osValuesWithStatus.length} Discraft OSL map records, calculated disc counts, and updated Excel file`,
            recordsProcessed: osValuesWithStatus.length,
            duration: `${duration}ms`,
            summary: {
                totalRecords: osValuesWithStatus.length,
                statusBreakdown: {
                    'In Stock': osValuesWithStatus.filter(item => item.status === 'In Stock').length,
                    'Out of Stock': osValuesWithStatus.filter(item => item.status === 'Out of Stock').length,
                    'Not Available': osValuesWithStatus.filter(item => item.status === 'Not Available').length
                },
                discCounts: discCountResults,
                excelUpdate: excelUpdateResults
            }
        };

        console.log(`[processImportDiscraftOslMapAndStatusTask] Task completed successfully: ${JSON.stringify(result)}`);
        await updateTaskStatus(task.id, 'completed', result);

    } catch (error) {
        const endTime = new Date();
        const duration = endTime - startTime;
        
        const errorResult = {
            success: false,
            error: error.message,
            duration: `${duration}ms`
        };

        console.error(`[processImportDiscraftOslMapAndStatusTask] Task failed: ${error.message}`);
        await logError(`Import Discraft OSL map task failed: ${error.message}`, `Task ${task.id}`);
        await updateTaskStatus(task.id, 'error', errorResult);
    }
}

export default processImportDiscraftOslMapAndStatusTask;
