import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace securesourcemanager_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Secure Source Manager API
     *
     * Regionally deployed, single-tenant managed source code repository hosted on Google Cloud.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const securesourcemanager = google.securesourcemanager('v1');
     * ```
     */
    export class Securesourcemanager {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * The request to batch create pull request comments.
     */
    export interface Schema$BatchCreatePullRequestCommentsRequest {
        /**
         * Required. The request message specifying the resources to create. There should be exactly one CreatePullRequestCommentRequest with CommentDetail being REVIEW in the list, and no more than 100 CreatePullRequestCommentRequests with CommentDetail being CODE in the list
         */
        requests?: Schema$CreatePullRequestCommentRequest[];
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/group/{group_id\}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/x`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/group/{group_id\}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/x`: All identities in a workload identity pool. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).
         */
        role?: string | null;
    }
    /**
     * Branch represents a branch involved in a pull request.
     */
    export interface Schema$Branch {
        /**
         * Required. Name of the branch.
         */
        ref?: string | null;
        /**
         * Output only. The commit at the tip of the branch.
         */
        sha?: string | null;
    }
    /**
     * Metadata of a BranchRule. BranchRule is the protection rule to enforce pre-defined rules on designated branches within a repository.
     */
    export interface Schema$BranchRule {
        /**
         * Optional. Determines if allow stale reviews or approvals before merging to the branch.
         */
        allowStaleReviews?: boolean | null;
        /**
         * Optional. User annotations. These attributes can only be set and used by the user. See https://google.aip.dev/128#annotations for more details such as format and size limitations.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Create timestamp.
         */
        createTime?: string | null;
        /**
         * Optional. Determines if the branch rule is disabled or not.
         */
        disabled?: boolean | null;
        /**
         * Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Optional. The pattern of the branch that can match to this BranchRule. Specified as regex. .* for all branches. Examples: main, (main|release.*). Current MVP phase only support `.*` for wildcard.
         */
        includePattern?: string | null;
        /**
         * Optional. The minimum number of approvals required for the branch rule to be matched.
         */
        minimumApprovalsCount?: number | null;
        /**
         * Optional. The minimum number of reviews required for the branch rule to be matched.
         */
        minimumReviewsCount?: number | null;
        /**
         * Optional. A unique identifier for a BranchRule. The name should be of the format: `projects/{project\}/locations/{location\}/repositories/{repository\}/branchRules/{branch_rule\}`
         */
        name?: string | null;
        /**
         * Optional. Determines if require comments resolved before merging to the branch.
         */
        requireCommentsResolved?: boolean | null;
        /**
         * Optional. List of required status checks before merging to the branch.
         */
        requiredStatusChecks?: Schema$Check[];
        /**
         * Optional. Determines if require linear history before merging to the branch.
         */
        requireLinearHistory?: boolean | null;
        /**
         * Optional. Determines if the branch rule requires a pull request or not.
         */
        requirePullRequest?: boolean | null;
        /**
         * Output only. Unique identifier of the repository.
         */
        uid?: string | null;
        /**
         * Output only. Update timestamp.
         */
        updateTime?: string | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * Check is a type for status check.
     */
    export interface Schema$Check {
        /**
         * Required. The context of the check.
         */
        context?: string | null;
    }
    /**
     * The request to close an issue.
     */
    export interface Schema$CloseIssueRequest {
        /**
         * Optional. The current etag of the issue. If the etag is provided and does not match the current etag of the issue, closing will be blocked and an ABORTED error will be returned.
         */
        etag?: string | null;
    }
    /**
     * ClosePullRequestRequest is the request to close a pull request.
     */
    export interface Schema$ClosePullRequestRequest {
    }
    /**
     * The comment on a code line.
     */
    export interface Schema$Code {
        /**
         * Required. The comment body.
         */
        body?: string | null;
        /**
         * Output only. The effective commit sha this code comment is pointing to.
         */
        effectiveCommitSha?: string | null;
        /**
         * Output only. The root comment of the conversation, derived from the reply field.
         */
        effectiveRootComment?: string | null;
        /**
         * Optional. The position of the comment.
         */
        position?: Schema$Position;
        /**
         * Optional. Input only. The PullRequestComment resource name that this comment is replying to.
         */
        reply?: string | null;
        /**
         * Output only. Boolean indicator if the comment is resolved.
         */
        resolved?: boolean | null;
    }
    /**
     * The general pull request comment.
     */
    export interface Schema$Comment {
        /**
         * Required. The comment body.
         */
        body?: string | null;
    }
    /**
     * The request to create a pull request comment.
     */
    export interface Schema$CreatePullRequestCommentRequest {
        /**
         * Required. The pull request in which to create the pull request comment. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         */
        parent?: string | null;
        /**
         * Required. The pull request comment to create.
         */
        pullRequestComment?: Schema$PullRequestComment;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * Response message containing the content of a blob.
     */
    export interface Schema$FetchBlobResponse {
        /**
         * The content of the blob, encoded as base64.
         */
        content?: string | null;
        /**
         * The SHA-1 hash of the blob.
         */
        sha?: string | null;
    }
    /**
     * Response message containing a list of TreeEntry objects.
     */
    export interface Schema$FetchTreeResponse {
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
        /**
         * The list of TreeEntry objects.
         */
        treeEntries?: Schema$TreeEntry[];
    }
    /**
     * Metadata of a FileDiff. FileDiff represents a single file diff in a pull request.
     */
    export interface Schema$FileDiff {
        /**
         * Output only. The action taken on the file (eg. added, modified, deleted).
         */
        action?: string | null;
        /**
         * Output only. The name of the file.
         */
        name?: string | null;
        /**
         * Output only. The git patch containing the file changes.
         */
        patch?: string | null;
        /**
         * Output only. The commit pointing to the file changes.
         */
        sha?: string | null;
    }
    /**
     * Metadata of a Secure Source Manager Hook.
     */
    export interface Schema$Hook {
        /**
         * Output only. Create timestamp.
         */
        createTime?: string | null;
        /**
         * Optional. Determines if the hook disabled or not. Set to true to stop sending traffic.
         */
        disabled?: boolean | null;
        /**
         * Optional. The events that trigger hook on.
         */
        events?: string[] | null;
        /**
         * Identifier. A unique identifier for a Hook. The name should be of the format: `projects/{project\}/locations/{location_id\}/repositories/{repository_id\}/hooks/{hook_id\}`
         */
        name?: string | null;
        /**
         * Optional. The trigger option for push events.
         */
        pushOption?: Schema$PushOption;
        /**
         * Optional. The sensitive query string to be appended to the target URI.
         */
        sensitiveQueryString?: string | null;
        /**
         * Required. The target URI to which the payloads will be delivered.
         */
        targetUri?: string | null;
        /**
         * Output only. Unique identifier of the hook.
         */
        uid?: string | null;
        /**
         * Output only. Update timestamp.
         */
        updateTime?: string | null;
    }
    /**
     * HostConfig has different instance endpoints.
     */
    export interface Schema$HostConfig {
        /**
         * Output only. API hostname.
         */
        api?: string | null;
        /**
         * Output only. Git HTTP hostname.
         */
        gitHttp?: string | null;
        /**
         * Output only. Git SSH hostname.
         */
        gitSsh?: string | null;
        /**
         * Output only. HTML hostname.
         */
        html?: string | null;
    }
    /**
     * Repository initialization configuration.
     */
    export interface Schema$InitialConfig {
        /**
         * Default branch name of the repository.
         */
        defaultBranch?: string | null;
        /**
         * List of gitignore template names user can choose from. Valid values: actionscript, ada, agda, android, anjuta, ansible, appcelerator-titanium, app-engine, archives, arch-linux-packages, atmel-studio, autotools, backup, bazaar, bazel, bitrix, bricx-cc, c, cake-php, calabash, cf-wheels, chef-cookbook, clojure, cloud9, c-make, code-igniter, code-kit, code-sniffer, common-lisp, composer, concrete5, coq, cordova, cpp, craft-cms, cuda, cvs, d, dart, dart-editor, delphi, diff, dm, dreamweaver, dropbox, drupal, drupal-7, eagle, eclipse, eiffel-studio, elisp, elixir, elm, emacs, ensime, epi-server, erlang, esp-idf, espresso, exercism, expression-engine, ext-js, fancy, finale, flex-builder, force-dot-com, fortran, fuel-php, gcov, git-book, gnome-shell-extension, go, godot, gpg, gradle, grails, gwt, haskell, hugo, iar-ewarm, idris, igor-pro, images, infor-cms, java, jboss, jboss-4, jboss-6, jdeveloper, jekyll, jenkins-home, jenv, jet-brains, jigsaw, joomla, julia, jupyter-notebooks, kate, kdevelop4, kentico, ki-cad, kohana, kotlin, lab-view, laravel, lazarus, leiningen, lemon-stand, libre-office, lilypond, linux, lithium, logtalk, lua, lyx, mac-os, magento, magento-1, magento-2, matlab, maven, mercurial, mercury, metals, meta-programming-system, meteor, microsoft-office, model-sim, momentics, mono-develop, nanoc, net-beans, nikola, nim, ninja, node, notepad-pp, nwjs, objective--c, ocaml, octave, opa, open-cart, openssl, oracle-forms, otto, packer, patch, perl, perl6, phalcon, phoenix, pimcore, play-framework, plone, prestashop, processing, psoc-creator, puppet, pure-script, putty, python, qooxdoo, qt, r, racket, rails, raku, red, redcar, redis, rhodes-rhomobile, ros, ruby, rust, sam, sass, sbt, scala, scheme, scons, scrivener, sdcc, seam-gen, sketch-up, slick-edit, smalltalk, snap, splunk, stata, stella, sublime-text, sugar-crm, svn, swift, symfony, symphony-cms, synopsys-vcs, tags, terraform, tex, text-mate, textpattern, think-php, tortoise-git, turbo-gears-2, typo3, umbraco, unity, unreal-engine, vagrant, vim, virtual-env, virtuoso, visual-studio, visual-studio-code, vue, vvvv, waf, web-methods, windows, word-press, xcode, xilinx, xilinx-ise, xojo, yeoman, yii, zend-framework, zephir.
         */
        gitignores?: string[] | null;
        /**
         * License template name user can choose from. Valid values: license-0bsd, license-389-exception, aal, abstyles, adobe-2006, adobe-glyph, adsl, afl-1-1, afl-1-2, afl-2-0, afl-2-1, afl-3-0, afmparse, agpl-1-0, agpl-1-0-only, agpl-1-0-or-later, agpl-3-0-only, agpl-3-0-or-later, aladdin, amdplpa, aml, ampas, antlr-pd, antlr-pd-fallback, apache-1-0, apache-1-1, apache-2-0, apafml, apl-1-0, apsl-1-0, apsl-1-1, apsl-1-2, apsl-2-0, artistic-1-0, artistic-1-0-cl8, artistic-1-0-perl, artistic-2-0, autoconf-exception-2-0, autoconf-exception-3-0, bahyph, barr, beerware, bison-exception-2-2, bittorrent-1-0, bittorrent-1-1, blessing, blueoak-1-0-0, bootloader-exception, borceux, bsd-1-clause, bsd-2-clause, bsd-2-clause-freebsd, bsd-2-clause-netbsd, bsd-2-clause-patent, bsd-2-clause-views, bsd-3-clause, bsd-3-clause-attribution, bsd-3-clause-clear, bsd-3-clause-lbnl, bsd-3-clause-modification, bsd-3-clause-no-nuclear-license, bsd-3-clause-no-nuclear-license-2014, bsd-3-clause-no-nuclear-warranty, bsd-3-clause-open-mpi, bsd-4-clause, bsd-4-clause-shortened, bsd-4-clause-uc, bsd-protection, bsd-source-code, bsl-1-0, busl-1-1, cal-1-0, cal-1-0-combined-work-exception, caldera, catosl-1-1, cc0-1-0, cc-by-1-0, cc-by-2-0, cc-by-3-0, cc-by-3-0-at, cc-by-3-0-us, cc-by-4-0, cc-by-nc-1-0, cc-by-nc-2-0, cc-by-nc-3-0, cc-by-nc-4-0, cc-by-nc-nd-1-0, cc-by-nc-nd-2-0, cc-by-nc-nd-3-0, cc-by-nc-nd-3-0-igo, cc-by-nc-nd-4-0, cc-by-nc-sa-1-0, cc-by-nc-sa-2-0, cc-by-nc-sa-3-0, cc-by-nc-sa-4-0, cc-by-nd-1-0, cc-by-nd-2-0, cc-by-nd-3-0, cc-by-nd-4-0, cc-by-sa-1-0, cc-by-sa-2-0, cc-by-sa-2-0-uk, cc-by-sa-2-1-jp, cc-by-sa-3-0, cc-by-sa-3-0-at, cc-by-sa-4-0, cc-pddc, cddl-1-0, cddl-1-1, cdla-permissive-1-0, cdla-sharing-1-0, cecill-1-0, cecill-1-1, cecill-2-0, cecill-2-1, cecill-b, cecill-c, cern-ohl-1-1, cern-ohl-1-2, cern-ohl-p-2-0, cern-ohl-s-2-0, cern-ohl-w-2-0, clartistic, classpath-exception-2-0, clisp-exception-2-0, cnri-jython, cnri-python, cnri-python-gpl-compatible, condor-1-1, copyleft-next-0-3-0, copyleft-next-0-3-1, cpal-1-0, cpl-1-0, cpol-1-02, crossword, crystal-stacker, cua-opl-1-0, cube, c-uda-1-0, curl, d-fsl-1-0, diffmark, digirule-foss-exception, doc, dotseqn, drl-1-0, dsdp, dvipdfm, ecl-1-0, ecl-2-0, ecos-exception-2-0, efl-1-0, efl-2-0, egenix, entessa, epics, epl-1-0, epl-2-0, erlpl-1-1, etalab-2-0, eu-datagrid, eupl-1-0, eupl-1-1, eupl-1-2, eurosym, fair, fawkes-runtime-exception, fltk-exception, font-exception-2-0, frameworx-1-0, freebsd-doc, freeimage, freertos-exception-2-0, fsfap, fsful, fsfullr, ftl, gcc-exception-2-0, gcc-exception-3-1, gd, gfdl-1-1-invariants-only, gfdl-1-1-invariants-or-later, gfdl-1-1-no-invariants-only, gfdl-1-1-no-invariants-or-later, gfdl-1-1-only, gfdl-1-1-or-later, gfdl-1-2-invariants-only, gfdl-1-2-invariants-or-later, gfdl-1-2-no-invariants-only, gfdl-1-2-no-invariants-or-later, gfdl-1-2-only, gfdl-1-2-or-later, gfdl-1-3-invariants-only, gfdl-1-3-invariants-or-later, gfdl-1-3-no-invariants-only, gfdl-1-3-no-invariants-or-later, gfdl-1-3-only, gfdl-1-3-or-later, giftware, gl2ps, glide, glulxe, glwtpl, gnu-javamail-exception, gnuplot, gpl-1-0-only, gpl-1-0-or-later, gpl-2-0-only, gpl-2-0-or-later, gpl-3-0-linking-exception, gpl-3-0-linking-source-exception, gpl-3-0-only, gpl-3-0-or-later, gpl-cc-1-0, gsoap-1-3b, haskell-report, hippocratic-2-1, hpnd, hpnd-sell-variant, htmltidy, i2p-gpl-java-exception, ibm-pibs, icu, ijg, image-magick, imatix, imlib2, info-zip, intel, intel-acpi, interbase-1-0, ipa, ipl-1-0, isc, jasper-2-0, jpnic, json, lal-1-2, lal-1-3, latex2e, leptonica, lgpl-2-0-only, lgpl-2-0-or-later, lgpl-2-1-only, lgpl-2-1-or-later, lgpl-3-0-linking-exception, lgpl-3-0-only, lgpl-3-0-or-later, lgpllr, libpng, libpng-2-0, libselinux-1-0, libtiff, libtool-exception, liliq-p-1-1, liliq-r-1-1, liliq-rplus-1-1, linux-openib, linux-syscall-note, llvm-exception, lpl-1-0, lpl-1-02, lppl-1-0, lppl-1-1, lppl-1-2, lppl-1-3a, lppl-1-3c, lzma-exception, make-index, mif-exception, miros, mit, mit-0, mit-advertising, mit-cmu, mit-enna, mit-feh, mit-modern-variant, mitnfa, mit-open-group, motosoto, mpich2, mpl-1-0, mpl-1-1, mpl-2-0, mpl-2-0-no-copyleft-exception, ms-pl, ms-rl, mtll, mulanpsl-1-0, mulanpsl-2-0, multics, mup, naist-2003, nasa-1-3, naumen, nbpl-1-0, ncgl-uk-2-0, ncsa, netcdf, net-snmp, newsletr, ngpl, nist-pd, nist-pd-fallback, nlod-1-0, nlpl, nokia, nokia-qt-exception-1-1, nosl, noweb, npl-1-0, npl-1-1, nposl-3-0, nrl, ntp, ntp-0, ocaml-lgpl-linking-exception, occt-exception-1-0, occt-pl, oclc-2-0, odbl-1-0, odc-by-1-0, ofl-1-0, ofl-1-0-no-rfn, ofl-1-0-rfn, ofl-1-1, ofl-1-1-no-rfn, ofl-1-1-rfn, ogc-1-0, ogdl-taiwan-1-0, ogl-canada-2-0, ogl-uk-1-0, ogl-uk-2-0, ogl-uk-3-0, ogtsl, oldap-1-1, oldap-1-2, oldap-1-3, oldap-1-4, oldap-2-0, oldap-2-0-1, oldap-2-1, oldap-2-2, oldap-2-2-1, oldap-2-2-2, oldap-2-3, oldap-2-4, oldap-2-7, oml, openjdk-assembly-exception-1-0, openssl, openvpn-openssl-exception, opl-1-0, oset-pl-2-1, osl-1-0, osl-1-1, osl-2-0, osl-2-1, osl-3-0, o-uda-1-0, parity-6-0-0, parity-7-0-0, pddl-1-0, php-3-0, php-3-01, plexus, polyform-noncommercial-1-0-0, polyform-small-business-1-0-0, postgresql, psf-2-0, psfrag, ps-or-pdf-font-exception-20170817, psutils, python-2-0, qhull, qpl-1-0, qt-gpl-exception-1-0, qt-lgpl-exception-1-1, qwt-exception-1-0, rdisc, rhecos-1-1, rpl-1-1, rpsl-1-0, rsa-md, rscpl, ruby, saxpath, sax-pd, scea, sendmail, sendmail-8-23, sgi-b-1-0, sgi-b-1-1, sgi-b-2-0, shl-0-51, shl-2-0, shl-2-1, simpl-2-0, sissl, sissl-1-2, sleepycat, smlnj, smppl, snia, spencer-86, spencer-94, spencer-99, spl-1-0, ssh-openssh, ssh-short, sspl-1-0, sugarcrm-1-1-3, swift-exception, swl, tapr-ohl-1-0, tcl, tcp-wrappers, tmate, torque-1-1, tosl, tu-berlin-1-0, tu-berlin-2-0, u-boot-exception-2-0, ucl-1-0, unicode-dfs-2015, unicode-dfs-2016, unicode-tou, universal-foss-exception-1-0, unlicense, upl-1-0, vim, vostrom, vsl-1-0, w3c, w3c-19980720, w3c-20150513, watcom-1-0, wsuipa, wtfpl, wxwindows-exception-3-1, x11, xerox, xfree86-1-1, xinetd, xnet, xpp, xskat, ypl-1-0, ypl-1-1, zed, zend-2-0, zimbra-1-3, zimbra-1-4, zlib, zlib-acknowledgement, zpl-1-1, zpl-2-0, zpl-2-1.
         */
        license?: string | null;
        /**
         * README template name. Valid template name(s) are: default.
         */
        readme?: string | null;
    }
    /**
     * A resource that represents a Secure Source Manager instance.
     */
    export interface Schema$Instance {
        /**
         * Output only. Create timestamp.
         */
        createTime?: string | null;
        /**
         * Output only. A list of hostnames for this instance.
         */
        hostConfig?: Schema$HostConfig;
        /**
         * Optional. Immutable. Customer-managed encryption key name, in the format projects/x/locations/x/keyRings/x/cryptoKeys/x.
         */
        kmsKey?: string | null;
        /**
         * Optional. Labels as key value pairs.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. A unique identifier for an instance. The name should be of the format: `projects/{project_number\}/locations/{location_id\}/instances/{instance_id\}` `project_number`: Maps to a unique int64 id assigned to each project. `location_id`: Refers to the region where the instance will be deployed. Since Secure Source Manager is a regional service, it must be one of the valid GCP regions. `instance_id`: User provided name for the instance, must be unique for a project_number and location_id combination.
         */
        name?: string | null;
        /**
         * Optional. Private settings for private instance.
         */
        privateConfig?: Schema$PrivateConfig;
        /**
         * Output only. Current state of the instance.
         */
        state?: string | null;
        /**
         * Output only. An optional field providing information about the current instance state.
         */
        stateNote?: string | null;
        /**
         * Output only. Update timestamp.
         */
        updateTime?: string | null;
        /**
         * Optional. Configuration for Workforce Identity Federation to support third party identity provider. If unset, defaults to the Google OIDC IdP.
         */
        workforceIdentityFederationConfig?: Schema$WorkforceIdentityFederationConfig;
    }
    /**
     * Metadata of an Issue.
     */
    export interface Schema$Issue {
        /**
         * Optional. Issue body. Provides a detailed description of the issue.
         */
        body?: string | null;
        /**
         * Output only. Close timestamp (if closed). Cleared when is re-opened.
         */
        closeTime?: string | null;
        /**
         * Output only. Creation timestamp.
         */
        createTime?: string | null;
        /**
         * Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Identifier. Unique identifier for an issue. The issue id is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/issues/{issue_id\}`
         */
        name?: string | null;
        /**
         * Output only. State of the issue.
         */
        state?: string | null;
        /**
         * Required. Issue title.
         */
        title?: string | null;
        /**
         * Output only. Last updated timestamp.
         */
        updateTime?: string | null;
    }
    /**
     * IssueComment represents a comment on an issue.
     */
    export interface Schema$IssueComment {
        /**
         * Required. The comment body.
         */
        body?: string | null;
        /**
         * Output only. Creation timestamp.
         */
        createTime?: string | null;
        /**
         * Identifier. Unique identifier for an issue comment. The comment id is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/issues/{issue\}/issueComments/{comment_id\}`
         */
        name?: string | null;
        /**
         * Output only. Last updated timestamp.
         */
        updateTime?: string | null;
    }
    /**
     * ListBranchRulesResponse is the response to listing branchRules.
     */
    export interface Schema$ListBranchRulesResponse {
        /**
         * The list of branch rules.
         */
        branchRules?: Schema$BranchRule[];
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
    }
    /**
     * ListHooksResponse is response to list hooks.
     */
    export interface Schema$ListHooksResponse {
        /**
         * The list of hooks.
         */
        hooks?: Schema$Hook[];
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
    }
    export interface Schema$ListInstancesResponse {
        /**
         * The list of instances.
         */
        instances?: Schema$Instance[];
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * The response to list issue comments.
     */
    export interface Schema$ListIssueCommentsResponse {
        /**
         * The list of issue comments.
         */
        issueComments?: Schema$IssueComment[];
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response to list issues.
     */
    export interface Schema$ListIssuesResponse {
        /**
         * The list of issues.
         */
        issues?: Schema$Issue[];
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * The response to list pull request comments.
     */
    export interface Schema$ListPullRequestCommentsResponse {
        /**
         * A token to set as page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The list of pull request comments.
         */
        pullRequestComments?: Schema$PullRequestComment[];
    }
    /**
     * ListPullRequestFileDiffsResponse is the response containing file diffs returned from ListPullRequestFileDiffs.
     */
    export interface Schema$ListPullRequestFileDiffsResponse {
        /**
         * The list of pull request file diffs.
         */
        fileDiffs?: Schema$FileDiff[];
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
    }
    /**
     * ListPullRequestsResponse is the response to list pull requests.
     */
    export interface Schema$ListPullRequestsResponse {
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
        /**
         * The list of pull requests.
         */
        pullRequests?: Schema$PullRequest[];
    }
    export interface Schema$ListRepositoriesResponse {
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
        /**
         * The list of repositories.
         */
        repositories?: Schema$Repository[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * MergePullRequestRequest is the request to merge a pull request.
     */
    export interface Schema$MergePullRequestRequest {
    }
    /**
     * The request to open an issue.
     */
    export interface Schema$OpenIssueRequest {
        /**
         * Optional. The current etag of the issue. If the etag is provided and does not match the current etag of the issue, opening will be blocked and an ABORTED error will be returned.
         */
        etag?: string | null;
    }
    /**
     * OpenPullRequestRequest is the request to open a pull request.
     */
    export interface Schema$OpenPullRequestRequest {
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$OperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * The position of the code comment.
     */
    export interface Schema$Position {
        /**
         * Required. The line number of the comment. Positive value means it's on the new side of the diff, negative value means it's on the old side.
         */
        line?: string | null;
        /**
         * Required. The path of the file.
         */
        path?: string | null;
    }
    /**
     * PrivateConfig includes settings for private instance.
     */
    export interface Schema$PrivateConfig {
        /**
         * Optional. Immutable. CA pool resource, resource must in the format of `projects/{project\}/locations/{location\}/caPools/{ca_pool\}`.
         */
        caPool?: string | null;
        /**
         * Output only. Service Attachment for HTTP, resource is in the format of `projects/{project\}/regions/{region\}/serviceAttachments/{service_attachment\}`.
         */
        httpServiceAttachment?: string | null;
        /**
         * Required. Immutable. Indicate if it's private instance.
         */
        isPrivate?: boolean | null;
        /**
         * Optional. Additional allowed projects for setting up PSC connections. Instance host project is automatically allowed and does not need to be included in this list.
         */
        pscAllowedProjects?: string[] | null;
        /**
         * Output only. Service Attachment for SSH, resource is in the format of `projects/{project\}/regions/{region\}/serviceAttachments/{service_attachment\}`.
         */
        sshServiceAttachment?: string | null;
    }
    /**
     * Metadata of a PullRequest. PullRequest is the request from a user to merge a branch (head) into another branch (base).
     */
    export interface Schema$PullRequest {
        /**
         * Required. The branch to merge changes in.
         */
        base?: Schema$Branch;
        /**
         * Optional. The pull request body. Provides a detailed description of the changes.
         */
        body?: string | null;
        /**
         * Output only. Close timestamp (if closed or merged). Cleared when pull request is re-opened.
         */
        closeTime?: string | null;
        /**
         * Output only. Creation timestamp.
         */
        createTime?: string | null;
        /**
         * Immutable. The branch containing the changes to be merged.
         */
        head?: Schema$Branch;
        /**
         * Output only. A unique identifier for a PullRequest. The number appended at the end is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/pullRequests/{pull_request_id\}`
         */
        name?: string | null;
        /**
         * Output only. State of the pull request (open, closed or merged).
         */
        state?: string | null;
        /**
         * Required. The pull request title.
         */
        title?: string | null;
        /**
         * Output only. Last updated timestamp.
         */
        updateTime?: string | null;
    }
    /**
     * PullRequestComment represents a comment on a pull request.
     */
    export interface Schema$PullRequestComment {
        /**
         * Optional. The comment on a code line.
         */
        code?: Schema$Code;
        /**
         * Optional. The general pull request comment.
         */
        comment?: Schema$Comment;
        /**
         * Output only. Creation timestamp.
         */
        createTime?: string | null;
        /**
         * Identifier. Unique identifier for the pull request comment. The comment id is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/pullRequests/{pull_request\}/pullRequestComments/{comment_id\}`
         */
        name?: string | null;
        /**
         * Optional. The review summary comment.
         */
        review?: Schema$Review;
        /**
         * Output only. Last updated timestamp.
         */
        updateTime?: string | null;
    }
    export interface Schema$PushOption {
        /**
         * Optional. Trigger hook for matching branches only. Specified as glob pattern. If empty or *, events for all branches are reported. Examples: main, {main,release*\}. See https://pkg.go.dev/github.com/gobwas/glob documentation.
         */
        branchFilter?: string | null;
    }
    /**
     * Metadata of a Secure Source Manager repository.
     */
    export interface Schema$Repository {
        /**
         * Output only. Create timestamp.
         */
        createTime?: string | null;
        /**
         * Optional. Description of the repository, which cannot exceed 500 characters.
         */
        description?: string | null;
        /**
         * Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Input only. Initial configurations for the repository.
         */
        initialConfig?: Schema$InitialConfig;
        /**
         * Optional. The name of the instance in which the repository is hosted, formatted as `projects/{project_number\}/locations/{location_id\}/instances/{instance_id\}` When creating repository via securesourcemanager.googleapis.com, this field is used as input. When creating repository via *.sourcemanager.dev, this field is output only.
         */
        instance?: string | null;
        /**
         * Optional. A unique identifier for a repository. The name should be of the format: `projects/{project\}/locations/{location_id\}/repositories/{repository_id\}`
         */
        name?: string | null;
        /**
         * Output only. Unique identifier of the repository.
         */
        uid?: string | null;
        /**
         * Output only. Update timestamp.
         */
        updateTime?: string | null;
        /**
         * Output only. URIs for the repository.
         */
        uris?: Schema$URIs;
    }
    /**
     * The request to resolve multiple pull request comments.
     */
    export interface Schema$ResolvePullRequestCommentsRequest {
        /**
         * Optional. If set, at least one comment in a thread is required, rest of the comments in the same thread will be automatically updated to resolved. If unset, all comments in the same thread need be present.
         */
        autoFill?: boolean | null;
        /**
         * Required. The names of the pull request comments to resolve. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}/pullRequestComments/{comment_id\}` Only comments from the same threads are allowed in the same request.
         */
        names?: string[] | null;
    }
    /**
     * The review summary comment.
     */
    export interface Schema$Review {
        /**
         * Required. The review action type.
         */
        actionType?: string | null;
        /**
         * Optional. The comment body.
         */
        body?: string | null;
        /**
         * Output only. The effective commit sha this review is pointing to.
         */
        effectiveCommitSha?: string | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * Represents an entry within a tree structure (like a Git tree).
     */
    export interface Schema$TreeEntry {
        /**
         * Output only. The file mode as a string (e.g., "100644"). Indicates file type. Output-only.
         */
        mode?: string | null;
        /**
         * Output only. The path of the file or directory within the tree (e.g., "src/main/java/MyClass.java"). Output-only.
         */
        path?: string | null;
        /**
         * Output only. The SHA-1 hash of the object (unique identifier). Output-only.
         */
        sha?: string | null;
        /**
         * Output only. The size of the object in bytes (only for blobs). Output-only.
         */
        size?: string | null;
        /**
         * Output only. The type of the object (TREE, BLOB, COMMIT). Output-only.
         */
        type?: string | null;
    }
    /**
     * The request to unresolve multiple pull request comments.
     */
    export interface Schema$UnresolvePullRequestCommentsRequest {
        /**
         * Optional. If set, at least one comment in a thread is required, rest of the comments in the same thread will be automatically updated to unresolved. If unset, all comments in the same thread need be present.
         */
        autoFill?: boolean | null;
        /**
         * Required. The names of the pull request comments to unresolve. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}/pullRequestComments/{comment_id\}` Only comments from the same threads are allowed in the same request.
         */
        names?: string[] | null;
    }
    /**
     * URIs for the repository.
     */
    export interface Schema$URIs {
        /**
         * Output only. API is the URI for API access.
         */
        api?: string | null;
        /**
         * Output only. git_https is the git HTTPS URI for git operations.
         */
        gitHttps?: string | null;
        /**
         * Output only. HTML is the URI for user to view the repository in a browser.
         */
        html?: string | null;
    }
    /**
     * WorkforceIdentityFederationConfig allows this instance to support users from external identity providers.
     */
    export interface Schema$WorkforceIdentityFederationConfig {
        /**
         * Optional. Immutable. Whether Workforce Identity Federation is enabled.
         */
        enabled?: boolean | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        instances: Resource$Projects$Locations$Instances;
        operations: Resource$Projects$Locations$Operations;
        repositories: Resource$Projects$Locations$Repositories;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.get({
         *     // Resource name for the location.
         *     name: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "displayName": "my_displayName",
         *   //   "labels": {},
         *   //   "locationId": "my_locationId",
         *   //   "metadata": {},
         *   //   "name": "my_name"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Location>>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.list({
         *     // Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         *     extraLocationTypes: 'placeholder-value',
         *     // A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         *     filter: 'placeholder-value',
         *     // The resource that owns the locations collection, if applicable.
         *     name: 'projects/my-project',
         *     // The maximum number of results to return. If not set, the service selects a default.
         *     pageSize: 'placeholder-value',
         *     // A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         *     pageToken: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "locations": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListLocationsResponse>>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         */
        extraLocationTypes?: string[];
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Instances {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new instance in a given project and location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.instances.create({
         *     // Required. ID of the instance to be created.
         *     instanceId: 'placeholder-value',
         *     // Required. Value for parent.
         *     parent: 'projects/my-project/locations/my-location',
         *     // Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         *     requestId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "createTime": "my_createTime",
         *       //   "hostConfig": {},
         *       //   "kmsKey": "my_kmsKey",
         *       //   "labels": {},
         *       //   "name": "my_name",
         *       //   "privateConfig": {},
         *       //   "state": "my_state",
         *       //   "stateNote": "my_stateNote",
         *       //   "updateTime": "my_updateTime",
         *       //   "workforceIdentityFederationConfig": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Instances$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single instance.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.instances.delete({
         *     // Required. Name of the resource.
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *     // Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         *     requestId: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Instances$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single instance.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.instances.get({
         *     // Required. Name of the resource.
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "createTime": "my_createTime",
         *   //   "hostConfig": {},
         *   //   "kmsKey": "my_kmsKey",
         *   //   "labels": {},
         *   //   "name": "my_name",
         *   //   "privateConfig": {},
         *   //   "state": "my_state",
         *   //   "stateNote": "my_stateNote",
         *   //   "updateTime": "my_updateTime",
         *   //   "workforceIdentityFederationConfig": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Instances$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Instance>>;
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: MethodOptions | BodyResponseCallback<Schema$Instance>, callback: BodyResponseCallback<Schema$Instance>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Get, callback: BodyResponseCallback<Schema$Instance>): void;
        get(callback: BodyResponseCallback<Schema$Instance>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.instances.getIamPolicy({
         *       // Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         *       'options.requestedPolicyVersion': 'placeholder-value',
         *       // REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *       resource:
         *         'projects/my-project/locations/my-location/instances/my-instance',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "auditConfigs": [],
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Instances$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists Instances in a given project and location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.instances.list({
         *     // Filter for filtering results.
         *     filter: 'placeholder-value',
         *     // Hint for how to order the results.
         *     orderBy: 'placeholder-value',
         *     // Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         *     pageSize: 'placeholder-value',
         *     // A token identifying a page of results the server should return.
         *     pageToken: 'placeholder-value',
         *     // Required. Parent value for ListInstancesRequest.
         *     parent: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "instances": [],
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Instances$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Instances$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListInstancesResponse>>;
        list(params: Params$Resource$Projects$Locations$Instances$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Instances$List, options: MethodOptions | BodyResponseCallback<Schema$ListInstancesResponse>, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Instances$List, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.instances.setIamPolicy({
         *       // REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *       resource:
         *         'projects/my-project/locations/my-location/instances/my-instance',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "policy": {},
         *         //   "updateMask": "my_updateMask"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "auditConfigs": [],
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Instances$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.instances.testIamPermissions({
         *       // REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *       resource:
         *         'projects/my-project/locations/my-location/instances/my-instance',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "permissions": []
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "permissions": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Instances$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Instances$Create extends StandardParameters {
        /**
         * Required. ID of the instance to be created.
         */
        instanceId?: string;
        /**
         * Required. Value for parent.
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Instance;
    }
    export interface Params$Resource$Projects$Locations$Instances$Delete extends StandardParameters {
        /**
         * Required. Name of the resource.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Get extends StandardParameters {
        /**
         * Required. Name of the resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$List extends StandardParameters {
        /**
         * Filter for filtering results.
         */
        filter?: string;
        /**
         * Hint for how to order the results.
         */
        orderBy?: string;
        /**
         * Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. Parent value for ListInstancesRequest.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.operations.cancel({
         *     // The name of the operation resource to be cancelled.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.operations.delete({
         *     // The name of the operation resource to be deleted.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.operations.get({
         *     // The name of the operation resource.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.operations.list({
         *     // The standard list filter.
         *     filter: 'placeholder-value',
         *     // The name of the operation's parent resource.
         *     name: 'projects/my-project/locations/my-location',
         *     // The standard list page size.
         *     pageSize: 'placeholder-value',
         *     // The standard list page token.
         *     pageToken: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "operations": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOperationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Repositories {
        context: APIRequestContext;
        branchRules: Resource$Projects$Locations$Repositories$Branchrules;
        hooks: Resource$Projects$Locations$Repositories$Hooks;
        issues: Resource$Projects$Locations$Repositories$Issues;
        pullRequests: Resource$Projects$Locations$Repositories$Pullrequests;
        constructor(context: APIRequestContext);
        /**
         * Creates a new repository in a given project and location. The Repository.Instance field is required in the request body for requests using the securesourcemanager.googleapis.com endpoint.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.repositories.create({
         *     // Required. The project in which to create the repository. Values are of the form `projects/{project_number\}/locations/{location_id\}`
         *     parent: 'projects/my-project/locations/my-location',
         *     // Required. The ID to use for the repository, which will become the final component of the repository's resource name. This value should be 4-63 characters, and valid characters are /a-z-/.
         *     repositoryId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "createTime": "my_createTime",
         *       //   "description": "my_description",
         *       //   "etag": "my_etag",
         *       //   "initialConfig": {},
         *       //   "instance": "my_instance",
         *       //   "name": "my_name",
         *       //   "uid": "my_uid",
         *       //   "updateTime": "my_updateTime",
         *       //   "uris": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Repositories$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Repositories$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Repositories$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a Repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.repositories.delete({
         *     // Optional. If set to true, and the repository is not found, the request will succeed but no action will be taken on the server.
         *     allowMissing: 'placeholder-value',
         *     // Required. Name of the repository to delete. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`.
         *     name: 'projects/my-project/locations/my-location/repositories/my-repositorie',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Repositories$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Repositories$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Repositories$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Fetches a blob from a repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.fetchBlob({
         *       // Required. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`. Specifies the repository containing the blob.
         *       repository:
         *         'projects/my-project/locations/my-location/repositories/my-repositorie',
         *       // Required. The SHA-1 hash of the blob to retrieve.
         *       sha: 'placeholder-value',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "content": "my_content",
         *   //   "sha": "my_sha"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        fetchBlob(params: Params$Resource$Projects$Locations$Repositories$Fetchblob, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        fetchBlob(params?: Params$Resource$Projects$Locations$Repositories$Fetchblob, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$FetchBlobResponse>>;
        fetchBlob(params: Params$Resource$Projects$Locations$Repositories$Fetchblob, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        fetchBlob(params: Params$Resource$Projects$Locations$Repositories$Fetchblob, options: MethodOptions | BodyResponseCallback<Schema$FetchBlobResponse>, callback: BodyResponseCallback<Schema$FetchBlobResponse>): void;
        fetchBlob(params: Params$Resource$Projects$Locations$Repositories$Fetchblob, callback: BodyResponseCallback<Schema$FetchBlobResponse>): void;
        fetchBlob(callback: BodyResponseCallback<Schema$FetchBlobResponse>): void;
        /**
         * Fetches a tree from a repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.fetchTree({
         *       // Optional. Requested page size. Server may return fewer items than requested. If unspecified, at most 10,000 items will be returned.
         *       pageSize: 'placeholder-value',
         *       // Optional. A token identifying a page of results the server should return.
         *       pageToken: 'placeholder-value',
         *       // Optional. If true, include all subfolders and their files in the response. If false, only the immediate children are returned.
         *       recursive: 'placeholder-value',
         *       // Optional. `ref` can be a SHA-1 hash, a branch name, or a tag. Specifies which tree to fetch. If not specified, the default branch will be used.
         *       ref: 'placeholder-value',
         *       // Required. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`. Specifies the repository to fetch the tree from.
         *       repository:
         *         'projects/my-project/locations/my-location/repositories/my-repositorie',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "treeEntries": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        fetchTree(params: Params$Resource$Projects$Locations$Repositories$Fetchtree, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        fetchTree(params?: Params$Resource$Projects$Locations$Repositories$Fetchtree, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$FetchTreeResponse>>;
        fetchTree(params: Params$Resource$Projects$Locations$Repositories$Fetchtree, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        fetchTree(params: Params$Resource$Projects$Locations$Repositories$Fetchtree, options: MethodOptions | BodyResponseCallback<Schema$FetchTreeResponse>, callback: BodyResponseCallback<Schema$FetchTreeResponse>): void;
        fetchTree(params: Params$Resource$Projects$Locations$Repositories$Fetchtree, callback: BodyResponseCallback<Schema$FetchTreeResponse>): void;
        fetchTree(callback: BodyResponseCallback<Schema$FetchTreeResponse>): void;
        /**
         * Gets metadata of a repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.repositories.get({
         *     // Required. Name of the repository to retrieve. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`.
         *     name: 'projects/my-project/locations/my-location/repositories/my-repositorie',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "createTime": "my_createTime",
         *   //   "description": "my_description",
         *   //   "etag": "my_etag",
         *   //   "initialConfig": {},
         *   //   "instance": "my_instance",
         *   //   "name": "my_name",
         *   //   "uid": "my_uid",
         *   //   "updateTime": "my_updateTime",
         *   //   "uris": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Repositories$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Repositories$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Repository>>;
        get(params: Params$Resource$Projects$Locations$Repositories$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Get, options: MethodOptions | BodyResponseCallback<Schema$Repository>, callback: BodyResponseCallback<Schema$Repository>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Get, callback: BodyResponseCallback<Schema$Repository>): void;
        get(callback: BodyResponseCallback<Schema$Repository>): void;
        /**
         * Get IAM policy for a repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.getIamPolicy({
         *       // Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         *       'options.requestedPolicyVersion': 'placeholder-value',
         *       // REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *       resource:
         *         'projects/my-project/locations/my-location/repositories/my-repositorie',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "auditConfigs": [],
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Repositories$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Repositories$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Repositories$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Repositories$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Repositories$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists Repositories in a given project and location. The instance field is required in the query parameter for requests using the securesourcemanager.googleapis.com endpoint.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.repositories.list({
         *     // Optional. Filter results.
         *     filter: 'placeholder-value',
         *     // Optional. The name of the instance in which the repository is hosted, formatted as `projects/{project_number\}/locations/{location_id\}/instances/{instance_id\}`. When listing repositories via securesourcemanager.googleapis.com, this field is required. When listing repositories via *.sourcemanager.dev, this field is ignored.
         *     instance: 'placeholder-value',
         *     // Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         *     pageSize: 'placeholder-value',
         *     // A token identifying a page of results the server should return.
         *     pageToken: 'placeholder-value',
         *     // Required. Parent value for ListRepositoriesRequest.
         *     parent: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "repositories": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Repositories$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Repositories$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListRepositoriesResponse>>;
        list(params: Params$Resource$Projects$Locations$Repositories$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$List, options: MethodOptions | BodyResponseCallback<Schema$ListRepositoriesResponse>, callback: BodyResponseCallback<Schema$ListRepositoriesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$List, callback: BodyResponseCallback<Schema$ListRepositoriesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListRepositoriesResponse>): void;
        /**
         * Updates the metadata of a repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await securesourcemanager.projects.locations.repositories.patch({
         *     // Optional. A unique identifier for a repository. The name should be of the format: `projects/{project\}/locations/{location_id\}/repositories/{repository_id\}`
         *     name: 'projects/my-project/locations/my-location/repositories/my-repositorie',
         *     // Optional. Field mask is used to specify the fields to be overwritten in the repository resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         *     updateMask: 'placeholder-value',
         *     // Optional. False by default. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.
         *     validateOnly: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "createTime": "my_createTime",
         *       //   "description": "my_description",
         *       //   "etag": "my_etag",
         *       //   "initialConfig": {},
         *       //   "instance": "my_instance",
         *       //   "name": "my_name",
         *       //   "uid": "my_uid",
         *       //   "updateTime": "my_updateTime",
         *       //   "uris": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Repositories$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Repositories$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Repositories$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Set IAM policy on a repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.setIamPolicy({
         *       // REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *       resource:
         *         'projects/my-project/locations/my-location/repositories/my-repositorie',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "policy": {},
         *         //   "updateMask": "my_updateMask"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "auditConfigs": [],
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Repositories$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Repositories$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Repositories$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Repositories$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Repositories$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Test IAM permissions on a repository. IAM permission checks are not required on this method.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.testIamPermissions(
         *       {
         *         // REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *         resource:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "permissions": []
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "permissions": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Repositories$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Repositories$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Repositories$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Repositories$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Repositories$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Create extends StandardParameters {
        /**
         * Required. The project in which to create the repository. Values are of the form `projects/{project_number\}/locations/{location_id\}`
         */
        parent?: string;
        /**
         * Required. The ID to use for the repository, which will become the final component of the repository's resource name. This value should be 4-63 characters, and valid characters are /a-z-/.
         */
        repositoryId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Repository;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Delete extends StandardParameters {
        /**
         * Optional. If set to true, and the repository is not found, the request will succeed but no action will be taken on the server.
         */
        allowMissing?: boolean;
        /**
         * Required. Name of the repository to delete. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Fetchblob extends StandardParameters {
        /**
         * Required. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`. Specifies the repository containing the blob.
         */
        repository?: string;
        /**
         * Required. The SHA-1 hash of the blob to retrieve.
         */
        sha?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Fetchtree extends StandardParameters {
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, at most 10,000 items will be returned.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Optional. If true, include all subfolders and their files in the response. If false, only the immediate children are returned.
         */
        recursive?: boolean;
        /**
         * Optional. `ref` can be a SHA-1 hash, a branch name, or a tag. Specifies which tree to fetch. If not specified, the default branch will be used.
         */
        ref?: string;
        /**
         * Required. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`. Specifies the repository to fetch the tree from.
         */
        repository?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Get extends StandardParameters {
        /**
         * Required. Name of the repository to retrieve. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$List extends StandardParameters {
        /**
         * Optional. Filter results.
         */
        filter?: string;
        /**
         * Optional. The name of the instance in which the repository is hosted, formatted as `projects/{project_number\}/locations/{location_id\}/instances/{instance_id\}`. When listing repositories via securesourcemanager.googleapis.com, this field is required. When listing repositories via *.sourcemanager.dev, this field is ignored.
         */
        instance?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. Parent value for ListRepositoriesRequest.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Patch extends StandardParameters {
        /**
         * Optional. A unique identifier for a repository. The name should be of the format: `projects/{project\}/locations/{location_id\}/repositories/{repository_id\}`
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the repository resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Optional. False by default. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Repository;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Repositories$Branchrules {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * CreateBranchRule creates a branch rule in a given repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.branchRules.create(
         *       {
         *         branchRuleId: 'placeholder-value',
         *
         *         parent:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "allowStaleReviews": false,
         *           //   "annotations": {},
         *           //   "createTime": "my_createTime",
         *           //   "disabled": false,
         *           //   "etag": "my_etag",
         *           //   "includePattern": "my_includePattern",
         *           //   "minimumApprovalsCount": 0,
         *           //   "minimumReviewsCount": 0,
         *           //   "name": "my_name",
         *           //   "requireCommentsResolved": false,
         *           //   "requireLinearHistory": false,
         *           //   "requirePullRequest": false,
         *           //   "requiredStatusChecks": [],
         *           //   "uid": "my_uid",
         *           //   "updateTime": "my_updateTime"
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Repositories$Branchrules$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * DeleteBranchRule deletes a branch rule.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.branchRules.delete(
         *       {
         *         // Optional. If set to true, and the branch rule is not found, the request will succeed but no action will be taken on the server.
         *         allowMissing: 'placeholder-value',
         *
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/branchRules/my-branchRule',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Repositories$Branchrules$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * GetBranchRule gets a branch rule.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.branchRules.get({
         *       // Required. Name of the repository to retrieve. The format is `projects/{project\}/locations/{location\}/repositories/{repository\}/branchRules/{branch_rule\}`.
         *       name: 'projects/my-project/locations/my-location/repositories/my-repositorie/branchRules/my-branchRule',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "allowStaleReviews": false,
         *   //   "annotations": {},
         *   //   "createTime": "my_createTime",
         *   //   "disabled": false,
         *   //   "etag": "my_etag",
         *   //   "includePattern": "my_includePattern",
         *   //   "minimumApprovalsCount": 0,
         *   //   "minimumReviewsCount": 0,
         *   //   "name": "my_name",
         *   //   "requireCommentsResolved": false,
         *   //   "requireLinearHistory": false,
         *   //   "requirePullRequest": false,
         *   //   "requiredStatusChecks": [],
         *   //   "uid": "my_uid",
         *   //   "updateTime": "my_updateTime"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Repositories$Branchrules$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$BranchRule>>;
        get(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Get, options: MethodOptions | BodyResponseCallback<Schema$BranchRule>, callback: BodyResponseCallback<Schema$BranchRule>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Get, callback: BodyResponseCallback<Schema$BranchRule>): void;
        get(callback: BodyResponseCallback<Schema$BranchRule>): void;
        /**
         * ListBranchRules lists branch rules in a given repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.branchRules.list({
         *       pageSize: 'placeholder-value',
         *
         *       pageToken: 'placeholder-value',
         *
         *       parent:
         *         'projects/my-project/locations/my-location/repositories/my-repositorie',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "branchRules": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Repositories$Branchrules$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Repositories$Branchrules$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListBranchRulesResponse>>;
        list(params: Params$Resource$Projects$Locations$Repositories$Branchrules$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Branchrules$List, options: MethodOptions | BodyResponseCallback<Schema$ListBranchRulesResponse>, callback: BodyResponseCallback<Schema$ListBranchRulesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Branchrules$List, callback: BodyResponseCallback<Schema$ListBranchRulesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListBranchRulesResponse>): void;
        /**
         * UpdateBranchRule updates a branch rule.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.branchRules.patch(
         *       {
         *         // Optional. A unique identifier for a BranchRule. The name should be of the format: `projects/{project\}/locations/{location\}/repositories/{repository\}/branchRules/{branch_rule\}`
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/branchRules/my-branchRule',
         *         // Required. Field mask is used to specify the fields to be overwritten in the branchRule resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. The special value "*" means full replacement.
         *         updateMask: 'placeholder-value',
         *         // Optional. If set, validate the request and preview the review, but do not actually post it. (https://google.aip.dev/163, for declarative friendly)
         *         validateOnly: 'placeholder-value',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "allowStaleReviews": false,
         *           //   "annotations": {},
         *           //   "createTime": "my_createTime",
         *           //   "disabled": false,
         *           //   "etag": "my_etag",
         *           //   "includePattern": "my_includePattern",
         *           //   "minimumApprovalsCount": 0,
         *           //   "minimumReviewsCount": 0,
         *           //   "name": "my_name",
         *           //   "requireCommentsResolved": false,
         *           //   "requireLinearHistory": false,
         *           //   "requirePullRequest": false,
         *           //   "requiredStatusChecks": [],
         *           //   "uid": "my_uid",
         *           //   "updateTime": "my_updateTime"
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Repositories$Branchrules$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Branchrules$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Branchrules$Create extends StandardParameters {
        /**
         *
         */
        branchRuleId?: string;
        /**
         *
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$BranchRule;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Branchrules$Delete extends StandardParameters {
        /**
         * Optional. If set to true, and the branch rule is not found, the request will succeed but no action will be taken on the server.
         */
        allowMissing?: boolean;
        /**
         *
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Branchrules$Get extends StandardParameters {
        /**
         * Required. Name of the repository to retrieve. The format is `projects/{project\}/locations/{location\}/repositories/{repository\}/branchRules/{branch_rule\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Branchrules$List extends StandardParameters {
        /**
         *
         */
        pageSize?: number;
        /**
         *
         */
        pageToken?: string;
        /**
         *
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Branchrules$Patch extends StandardParameters {
        /**
         * Optional. A unique identifier for a BranchRule. The name should be of the format: `projects/{project\}/locations/{location\}/repositories/{repository\}/branchRules/{branch_rule\}`
         */
        name?: string;
        /**
         * Required. Field mask is used to specify the fields to be overwritten in the branchRule resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. The special value "*" means full replacement.
         */
        updateMask?: string;
        /**
         * Optional. If set, validate the request and preview the review, but do not actually post it. (https://google.aip.dev/163, for declarative friendly)
         */
        validateOnly?: boolean;
        /**
         * Request body metadata
         */
        requestBody?: Schema$BranchRule;
    }
    export class Resource$Projects$Locations$Repositories$Hooks {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new hook in a given repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.hooks.create({
         *       // Required. The ID to use for the hook, which will become the final component of the hook's resource name. This value restricts to lower-case letters, numbers, and hyphen, with the first character a letter, the last a letter or a number, and a 63 character maximum.
         *       hookId: 'placeholder-value',
         *       // Required. The repository in which to create the hook. Values are of the form `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`
         *       parent:
         *         'projects/my-project/locations/my-location/repositories/my-repositorie',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "createTime": "my_createTime",
         *         //   "disabled": false,
         *         //   "events": [],
         *         //   "name": "my_name",
         *         //   "pushOption": {},
         *         //   "sensitiveQueryString": "my_sensitiveQueryString",
         *         //   "targetUri": "my_targetUri",
         *         //   "uid": "my_uid",
         *         //   "updateTime": "my_updateTime"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Repositories$Hooks$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Repositories$Hooks$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Repositories$Hooks$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Hooks$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Hooks$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a Hook.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.hooks.delete({
         *       // Required. Name of the hook to delete. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/hooks/{hook_id\}`.
         *       name: 'projects/my-project/locations/my-location/repositories/my-repositorie/hooks/my-hook',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Repositories$Hooks$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Repositories$Hooks$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Repositories$Hooks$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Hooks$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Hooks$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets metadata of a hook.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.hooks.get({
         *       // Required. Name of the hook to retrieve. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/hooks/{hook_id\}`.
         *       name: 'projects/my-project/locations/my-location/repositories/my-repositorie/hooks/my-hook',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "createTime": "my_createTime",
         *   //   "disabled": false,
         *   //   "events": [],
         *   //   "name": "my_name",
         *   //   "pushOption": {},
         *   //   "sensitiveQueryString": "my_sensitiveQueryString",
         *   //   "targetUri": "my_targetUri",
         *   //   "uid": "my_uid",
         *   //   "updateTime": "my_updateTime"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Repositories$Hooks$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Repositories$Hooks$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Hook>>;
        get(params: Params$Resource$Projects$Locations$Repositories$Hooks$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Hooks$Get, options: MethodOptions | BodyResponseCallback<Schema$Hook>, callback: BodyResponseCallback<Schema$Hook>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Hooks$Get, callback: BodyResponseCallback<Schema$Hook>): void;
        get(callback: BodyResponseCallback<Schema$Hook>): void;
        /**
         * Lists hooks in a given repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.hooks.list({
         *       // Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         *       pageSize: 'placeholder-value',
         *       // Optional. A token identifying a page of results the server should return.
         *       pageToken: 'placeholder-value',
         *       // Required. Parent value for ListHooksRequest.
         *       parent:
         *         'projects/my-project/locations/my-location/repositories/my-repositorie',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "hooks": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Repositories$Hooks$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Repositories$Hooks$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListHooksResponse>>;
        list(params: Params$Resource$Projects$Locations$Repositories$Hooks$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Hooks$List, options: MethodOptions | BodyResponseCallback<Schema$ListHooksResponse>, callback: BodyResponseCallback<Schema$ListHooksResponse>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Hooks$List, callback: BodyResponseCallback<Schema$ListHooksResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListHooksResponse>): void;
        /**
         * Updates the metadata of a hook.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.hooks.patch({
         *       // Identifier. A unique identifier for a Hook. The name should be of the format: `projects/{project\}/locations/{location_id\}/repositories/{repository_id\}/hooks/{hook_id\}`
         *       name: 'projects/my-project/locations/my-location/repositories/my-repositorie/hooks/my-hook',
         *       // Required. Field mask is used to specify the fields to be overwritten in the hook resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. The special value "*" means full replacement.
         *       updateMask: 'placeholder-value',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "createTime": "my_createTime",
         *         //   "disabled": false,
         *         //   "events": [],
         *         //   "name": "my_name",
         *         //   "pushOption": {},
         *         //   "sensitiveQueryString": "my_sensitiveQueryString",
         *         //   "targetUri": "my_targetUri",
         *         //   "uid": "my_uid",
         *         //   "updateTime": "my_updateTime"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Repositories$Hooks$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Repositories$Hooks$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Repositories$Hooks$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Hooks$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Hooks$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Hooks$Create extends StandardParameters {
        /**
         * Required. The ID to use for the hook, which will become the final component of the hook's resource name. This value restricts to lower-case letters, numbers, and hyphen, with the first character a letter, the last a letter or a number, and a 63 character maximum.
         */
        hookId?: string;
        /**
         * Required. The repository in which to create the hook. Values are of the form `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Hook;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Hooks$Delete extends StandardParameters {
        /**
         * Required. Name of the hook to delete. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/hooks/{hook_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Hooks$Get extends StandardParameters {
        /**
         * Required. Name of the hook to retrieve. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/hooks/{hook_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Hooks$List extends StandardParameters {
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. Parent value for ListHooksRequest.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Hooks$Patch extends StandardParameters {
        /**
         * Identifier. A unique identifier for a Hook. The name should be of the format: `projects/{project\}/locations/{location_id\}/repositories/{repository_id\}/hooks/{hook_id\}`
         */
        name?: string;
        /**
         * Required. Field mask is used to specify the fields to be overwritten in the hook resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. The special value "*" means full replacement.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Hook;
    }
    export class Resource$Projects$Locations$Repositories$Issues {
        context: APIRequestContext;
        issueComments: Resource$Projects$Locations$Repositories$Issues$Issuecomments;
        constructor(context: APIRequestContext);
        /**
         * Closes an issue.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.close({
         *       // Required. Name of the issue to close. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}`.
         *       name: 'projects/my-project/locations/my-location/repositories/my-repositorie/issues/my-issue',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "etag": "my_etag"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        close(params: Params$Resource$Projects$Locations$Repositories$Issues$Close, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        close(params?: Params$Resource$Projects$Locations$Repositories$Issues$Close, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        close(params: Params$Resource$Projects$Locations$Repositories$Issues$Close, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        close(params: Params$Resource$Projects$Locations$Repositories$Issues$Close, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        close(params: Params$Resource$Projects$Locations$Repositories$Issues$Close, callback: BodyResponseCallback<Schema$Operation>): void;
        close(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Creates an issue.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.create({
         *       // Required. The repository in which to create the issue. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`
         *       parent:
         *         'projects/my-project/locations/my-location/repositories/my-repositorie',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "body": "my_body",
         *         //   "closeTime": "my_closeTime",
         *         //   "createTime": "my_createTime",
         *         //   "etag": "my_etag",
         *         //   "name": "my_name",
         *         //   "state": "my_state",
         *         //   "title": "my_title",
         *         //   "updateTime": "my_updateTime"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Repositories$Issues$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Repositories$Issues$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Repositories$Issues$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Issues$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Issues$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes an issue.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.delete({
         *       // Optional. The current etag of the issue. If the etag is provided and does not match the current etag of the issue, deletion will be blocked and an ABORTED error will be returned.
         *       etag: 'placeholder-value',
         *       // Required. Name of the issue to delete. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}`.
         *       name: 'projects/my-project/locations/my-location/repositories/my-repositorie/issues/my-issue',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Repositories$Issues$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Repositories$Issues$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Repositories$Issues$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Issues$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Issues$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets an issue.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.get({
         *       // Required. Name of the issue to retrieve. The format is `projects/{project\}/locations/{location\}/repositories/{repository\}/issues/{issue_id\}`.
         *       name: 'projects/my-project/locations/my-location/repositories/my-repositorie/issues/my-issue',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "body": "my_body",
         *   //   "closeTime": "my_closeTime",
         *   //   "createTime": "my_createTime",
         *   //   "etag": "my_etag",
         *   //   "name": "my_name",
         *   //   "state": "my_state",
         *   //   "title": "my_title",
         *   //   "updateTime": "my_updateTime"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Repositories$Issues$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Repositories$Issues$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Issue>>;
        get(params: Params$Resource$Projects$Locations$Repositories$Issues$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Issues$Get, options: MethodOptions | BodyResponseCallback<Schema$Issue>, callback: BodyResponseCallback<Schema$Issue>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Issues$Get, callback: BodyResponseCallback<Schema$Issue>): void;
        get(callback: BodyResponseCallback<Schema$Issue>): void;
        /**
         * Lists issues in a repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.list({
         *       // Optional. Used to filter the resulting issues list.
         *       filter: 'placeholder-value',
         *       // Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         *       pageSize: 'placeholder-value',
         *       // Optional. A token identifying a page of results the server should return.
         *       pageToken: 'placeholder-value',
         *       // Required. The repository in which to list issues. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`
         *       parent:
         *         'projects/my-project/locations/my-location/repositories/my-repositorie',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "issues": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Repositories$Issues$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Repositories$Issues$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListIssuesResponse>>;
        list(params: Params$Resource$Projects$Locations$Repositories$Issues$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Issues$List, options: MethodOptions | BodyResponseCallback<Schema$ListIssuesResponse>, callback: BodyResponseCallback<Schema$ListIssuesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Issues$List, callback: BodyResponseCallback<Schema$ListIssuesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListIssuesResponse>): void;
        /**
         * Opens an issue.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.open({
         *       // Required. Name of the issue to open. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}`.
         *       name: 'projects/my-project/locations/my-location/repositories/my-repositorie/issues/my-issue',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "etag": "my_etag"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        open(params: Params$Resource$Projects$Locations$Repositories$Issues$Open, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        open(params?: Params$Resource$Projects$Locations$Repositories$Issues$Open, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        open(params: Params$Resource$Projects$Locations$Repositories$Issues$Open, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        open(params: Params$Resource$Projects$Locations$Repositories$Issues$Open, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        open(params: Params$Resource$Projects$Locations$Repositories$Issues$Open, callback: BodyResponseCallback<Schema$Operation>): void;
        open(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Updates a issue.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.patch({
         *       // Identifier. Unique identifier for an issue. The issue id is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/issues/{issue_id\}`
         *       name: 'projects/my-project/locations/my-location/repositories/my-repositorie/issues/my-issue',
         *       // Optional. Field mask is used to specify the fields to be overwritten in the issue resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. The special value "*" means full replacement.
         *       updateMask: 'placeholder-value',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "body": "my_body",
         *         //   "closeTime": "my_closeTime",
         *         //   "createTime": "my_createTime",
         *         //   "etag": "my_etag",
         *         //   "name": "my_name",
         *         //   "state": "my_state",
         *         //   "title": "my_title",
         *         //   "updateTime": "my_updateTime"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Repositories$Issues$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Repositories$Issues$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Repositories$Issues$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Issues$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Issues$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Close extends StandardParameters {
        /**
         * Required. Name of the issue to close. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CloseIssueRequest;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Create extends StandardParameters {
        /**
         * Required. The repository in which to create the issue. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Issue;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Delete extends StandardParameters {
        /**
         * Optional. The current etag of the issue. If the etag is provided and does not match the current etag of the issue, deletion will be blocked and an ABORTED error will be returned.
         */
        etag?: string;
        /**
         * Required. Name of the issue to delete. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Get extends StandardParameters {
        /**
         * Required. Name of the issue to retrieve. The format is `projects/{project\}/locations/{location\}/repositories/{repository\}/issues/{issue_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$List extends StandardParameters {
        /**
         * Optional. Used to filter the resulting issues list.
         */
        filter?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. The repository in which to list issues. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Open extends StandardParameters {
        /**
         * Required. Name of the issue to open. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$OpenIssueRequest;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Patch extends StandardParameters {
        /**
         * Identifier. Unique identifier for an issue. The issue id is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/issues/{issue_id\}`
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the issue resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. The special value "*" means full replacement.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Issue;
    }
    export class Resource$Projects$Locations$Repositories$Issues$Issuecomments {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates an issue comment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.issueComments.create(
         *       {
         *         // Required. The issue in which to create the issue comment. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}`
         *         parent:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie/issues/my-issue',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "body": "my_body",
         *           //   "createTime": "my_createTime",
         *           //   "name": "my_name",
         *           //   "updateTime": "my_updateTime"
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes an issue comment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.issueComments.delete(
         *       {
         *         // Required. Name of the issue comment to delete. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}/issueComments/{comment_id\}`.
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/issues/my-issue/issueComments/my-issueComment',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets an issue comment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.issueComments.get(
         *       {
         *         // Required. Name of the issue comment to retrieve. The format is `projects/{project\}/locations/{location\}/repositories/{repository\}/issues/{issue_id\}/issueComments/{comment_id\}`.
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/issues/my-issue/issueComments/my-issueComment',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "body": "my_body",
         *   //   "createTime": "my_createTime",
         *   //   "name": "my_name",
         *   //   "updateTime": "my_updateTime"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$IssueComment>>;
        get(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Get, options: MethodOptions | BodyResponseCallback<Schema$IssueComment>, callback: BodyResponseCallback<Schema$IssueComment>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Get, callback: BodyResponseCallback<Schema$IssueComment>): void;
        get(callback: BodyResponseCallback<Schema$IssueComment>): void;
        /**
         * Lists comments in an issue.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.issueComments.list(
         *       {
         *         // Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         *         pageSize: 'placeholder-value',
         *         // Optional. A token identifying a page of results the server should return.
         *         pageToken: 'placeholder-value',
         *         // Required. The issue in which to list the comments. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}`
         *         parent:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie/issues/my-issue',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "issueComments": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListIssueCommentsResponse>>;
        list(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$List, options: MethodOptions | BodyResponseCallback<Schema$ListIssueCommentsResponse>, callback: BodyResponseCallback<Schema$ListIssueCommentsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$List, callback: BodyResponseCallback<Schema$ListIssueCommentsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListIssueCommentsResponse>): void;
        /**
         * Updates an issue comment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.issues.issueComments.patch(
         *       {
         *         // Identifier. Unique identifier for an issue comment. The comment id is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/issues/{issue\}/issueComments/{comment_id\}`
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/issues/my-issue/issueComments/my-issueComment',
         *         // Optional. Field mask is used to specify the fields to be overwritten in the issue comment resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. The special value "*" means full replacement.
         *         updateMask: 'placeholder-value',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "body": "my_body",
         *           //   "createTime": "my_createTime",
         *           //   "name": "my_name",
         *           //   "updateTime": "my_updateTime"
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Create extends StandardParameters {
        /**
         * Required. The issue in which to create the issue comment. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$IssueComment;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Delete extends StandardParameters {
        /**
         * Required. Name of the issue comment to delete. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}/issueComments/{comment_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Get extends StandardParameters {
        /**
         * Required. Name of the issue comment to retrieve. The format is `projects/{project\}/locations/{location\}/repositories/{repository\}/issues/{issue_id\}/issueComments/{comment_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$List extends StandardParameters {
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. The issue in which to list the comments. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/issues/{issue_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Issues$Issuecomments$Patch extends StandardParameters {
        /**
         * Identifier. Unique identifier for an issue comment. The comment id is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/issues/{issue\}/issueComments/{comment_id\}`
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the issue comment resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. The special value "*" means full replacement.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$IssueComment;
    }
    export class Resource$Projects$Locations$Repositories$Pullrequests {
        context: APIRequestContext;
        pullRequestComments: Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments;
        constructor(context: APIRequestContext);
        /**
         * Closes a pull request without merging.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.close(
         *       {
         *         // Required. The pull request to close. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {}
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        close(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Close, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        close(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Close, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        close(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Close, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        close(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Close, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        close(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Close, callback: BodyResponseCallback<Schema$Operation>): void;
        close(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Creates a pull request.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.create(
         *       {
         *         // Required. The repository that the pull request is created from. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`
         *         parent:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "base": {},
         *           //   "body": "my_body",
         *           //   "closeTime": "my_closeTime",
         *           //   "createTime": "my_createTime",
         *           //   "head": {},
         *           //   "name": "my_name",
         *           //   "state": "my_state",
         *           //   "title": "my_title",
         *           //   "updateTime": "my_updateTime"
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a pull request.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.get({
         *       // Required. Name of the pull request to retrieve. The format is `projects/{project\}/locations/{location\}/repositories/{repository\}/pullRequests/{pull_request\}`.
         *       name: 'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "base": {},
         *   //   "body": "my_body",
         *   //   "closeTime": "my_closeTime",
         *   //   "createTime": "my_createTime",
         *   //   "head": {},
         *   //   "name": "my_name",
         *   //   "state": "my_state",
         *   //   "title": "my_title",
         *   //   "updateTime": "my_updateTime"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$PullRequest>>;
        get(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Get, options: MethodOptions | BodyResponseCallback<Schema$PullRequest>, callback: BodyResponseCallback<Schema$PullRequest>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Get, callback: BodyResponseCallback<Schema$PullRequest>): void;
        get(callback: BodyResponseCallback<Schema$PullRequest>): void;
        /**
         * Lists pull requests in a repository.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.list(
         *       {
         *         // Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         *         pageSize: 'placeholder-value',
         *         // Optional. A token identifying a page of results the server should return.
         *         pageToken: 'placeholder-value',
         *         // Required. The repository in which to list pull requests. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`
         *         parent:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "pullRequests": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListPullRequestsResponse>>;
        list(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$List, options: MethodOptions | BodyResponseCallback<Schema$ListPullRequestsResponse>, callback: BodyResponseCallback<Schema$ListPullRequestsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$List, callback: BodyResponseCallback<Schema$ListPullRequestsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListPullRequestsResponse>): void;
        /**
         * Lists a pull request's file diffs.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.listFileDiffs(
         *       {
         *         // Required. The pull request to list file diffs for. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *         // Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         *         pageSize: 'placeholder-value',
         *         // Optional. A token identifying a page of results the server should return.
         *         pageToken: 'placeholder-value',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "fileDiffs": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listFileDiffs(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Listfilediffs, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listFileDiffs(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Listfilediffs, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListPullRequestFileDiffsResponse>>;
        listFileDiffs(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Listfilediffs, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listFileDiffs(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Listfilediffs, options: MethodOptions | BodyResponseCallback<Schema$ListPullRequestFileDiffsResponse>, callback: BodyResponseCallback<Schema$ListPullRequestFileDiffsResponse>): void;
        listFileDiffs(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Listfilediffs, callback: BodyResponseCallback<Schema$ListPullRequestFileDiffsResponse>): void;
        listFileDiffs(callback: BodyResponseCallback<Schema$ListPullRequestFileDiffsResponse>): void;
        /**
         * Merges a pull request.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.merge(
         *       {
         *         // Required. The pull request to merge. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {}
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        merge(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Merge, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        merge(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Merge, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        merge(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Merge, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        merge(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Merge, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        merge(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Merge, callback: BodyResponseCallback<Schema$Operation>): void;
        merge(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Opens a pull request.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.open(
         *       {
         *         // Required. The pull request to open. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {}
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        open(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Open, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        open(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Open, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        open(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Open, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        open(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Open, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        open(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Open, callback: BodyResponseCallback<Schema$Operation>): void;
        open(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Updates a pull request.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.patch(
         *       {
         *         // Output only. A unique identifier for a PullRequest. The number appended at the end is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/pullRequests/{pull_request_id\}`
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *         // Optional. Field mask is used to specify the fields to be overwritten in the pull request resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. The special value "*" means full replacement.
         *         updateMask: 'placeholder-value',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "base": {},
         *           //   "body": "my_body",
         *           //   "closeTime": "my_closeTime",
         *           //   "createTime": "my_createTime",
         *           //   "head": {},
         *           //   "name": "my_name",
         *           //   "state": "my_state",
         *           //   "title": "my_title",
         *           //   "updateTime": "my_updateTime"
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Close extends StandardParameters {
        /**
         * Required. The pull request to close. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ClosePullRequestRequest;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Create extends StandardParameters {
        /**
         * Required. The repository that the pull request is created from. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$PullRequest;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Get extends StandardParameters {
        /**
         * Required. Name of the pull request to retrieve. The format is `projects/{project\}/locations/{location\}/repositories/{repository\}/pullRequests/{pull_request\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$List extends StandardParameters {
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. The repository in which to list pull requests. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Listfilediffs extends StandardParameters {
        /**
         * Required. The pull request to list file diffs for. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         */
        name?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results the server should return.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Merge extends StandardParameters {
        /**
         * Required. The pull request to merge. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MergePullRequestRequest;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Open extends StandardParameters {
        /**
         * Required. The pull request to open. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$OpenPullRequestRequest;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Patch extends StandardParameters {
        /**
         * Output only. A unique identifier for a PullRequest. The number appended at the end is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/pullRequests/{pull_request_id\}`
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the pull request resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. The special value "*" means full replacement.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$PullRequest;
    }
    export class Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Batch creates pull request comments.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.batchCreate(
         *       {
         *         // Required. The pull request in which to create the pull request comments. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         *         parent:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "requests": []
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        batchCreate(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Batchcreate, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        batchCreate(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Batchcreate, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        batchCreate(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Batchcreate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        batchCreate(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Batchcreate, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        batchCreate(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Batchcreate, callback: BodyResponseCallback<Schema$Operation>): void;
        batchCreate(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Creates a pull request comment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.create(
         *       {
         *         // Required. The pull request in which to create the pull request comment. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         *         parent:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "code": {},
         *           //   "comment": {},
         *           //   "createTime": "my_createTime",
         *           //   "name": "my_name",
         *           //   "review": {},
         *           //   "updateTime": "my_updateTime"
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a pull request comment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.delete(
         *       {
         *         // Required. Name of the pull request comment to delete. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}/pullRequestComments/{comment_id\}`.
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest/pullRequestComments/my-pullRequestComment',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a pull request comment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.get(
         *       {
         *         // Required. Name of the pull request comment to retrieve. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}/pullRequestComments/{comment_id\}`.
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest/pullRequestComments/my-pullRequestComment',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "code": {},
         *   //   "comment": {},
         *   //   "createTime": "my_createTime",
         *   //   "name": "my_name",
         *   //   "review": {},
         *   //   "updateTime": "my_updateTime"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$PullRequestComment>>;
        get(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Get, options: MethodOptions | BodyResponseCallback<Schema$PullRequestComment>, callback: BodyResponseCallback<Schema$PullRequestComment>): void;
        get(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Get, callback: BodyResponseCallback<Schema$PullRequestComment>): void;
        get(callback: BodyResponseCallback<Schema$PullRequestComment>): void;
        /**
         * Lists pull request comments.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.list(
         *       {
         *         // Optional. Requested page size. If unspecified, at most 100 pull request comments will be returned. The maximum value is 100; values above 100 will be coerced to 100.
         *         pageSize: 'placeholder-value',
         *         // Optional. A token identifying a page of results the server should return.
         *         pageToken: 'placeholder-value',
         *         // Required. The pull request in which to list pull request comments. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         *         parent:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "pullRequestComments": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListPullRequestCommentsResponse>>;
        list(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$List, options: MethodOptions | BodyResponseCallback<Schema$ListPullRequestCommentsResponse>, callback: BodyResponseCallback<Schema$ListPullRequestCommentsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$List, callback: BodyResponseCallback<Schema$ListPullRequestCommentsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListPullRequestCommentsResponse>): void;
        /**
         * Updates a pull request comment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.patch(
         *       {
         *         // Identifier. Unique identifier for the pull request comment. The comment id is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/pullRequests/{pull_request\}/pullRequestComments/{comment_id\}`
         *         name: 'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest/pullRequestComments/my-pullRequestComment',
         *         // Optional. Field mask is used to specify the fields to be overwritten in the pull request comment resource by the update. Updatable fields are `body`.
         *         updateMask: 'placeholder-value',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "code": {},
         *           //   "comment": {},
         *           //   "createTime": "my_createTime",
         *           //   "name": "my_name",
         *           //   "review": {},
         *           //   "updateTime": "my_updateTime"
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Resolves pull request comments.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.resolve(
         *       {
         *         // Required. The pull request in which to resolve the pull request comments. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         *         parent:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "autoFill": false,
         *           //   "names": []
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        resolve(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Resolve, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        resolve(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Resolve, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        resolve(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Resolve, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        resolve(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Resolve, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        resolve(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Resolve, callback: BodyResponseCallback<Schema$Operation>): void;
        resolve(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Unresolves pull request comment.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/securesourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const securesourcemanager = google.securesourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.unresolve(
         *       {
         *         // Required. The pull request in which to resolve the pull request comments. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         *         parent:
         *           'projects/my-project/locations/my-location/repositories/my-repositorie/pullRequests/my-pullRequest',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "autoFill": false,
         *           //   "names": []
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        unresolve(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Unresolve, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        unresolve(params?: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Unresolve, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        unresolve(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Unresolve, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        unresolve(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Unresolve, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        unresolve(params: Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Unresolve, callback: BodyResponseCallback<Schema$Operation>): void;
        unresolve(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Batchcreate extends StandardParameters {
        /**
         * Required. The pull request in which to create the pull request comments. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$BatchCreatePullRequestCommentsRequest;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Create extends StandardParameters {
        /**
         * Required. The pull request in which to create the pull request comment. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$PullRequestComment;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Delete extends StandardParameters {
        /**
         * Required. Name of the pull request comment to delete. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}/pullRequestComments/{comment_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Get extends StandardParameters {
        /**
         * Required. Name of the pull request comment to retrieve. The format is `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}/pullRequestComments/{comment_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$List extends StandardParameters {
        /**
         * Optional. Requested page size. If unspecified, at most 100 pull request comments will be returned. The maximum value is 100; values above 100 will be coerced to 100.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. The pull request in which to list pull request comments. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Patch extends StandardParameters {
        /**
         * Identifier. Unique identifier for the pull request comment. The comment id is generated by the server. Format: `projects/{project\}/locations/{location\}/repositories/{repository\}/pullRequests/{pull_request\}/pullRequestComments/{comment_id\}`
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the pull request comment resource by the update. Updatable fields are `body`.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$PullRequestComment;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Resolve extends StandardParameters {
        /**
         * Required. The pull request in which to resolve the pull request comments. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ResolvePullRequestCommentsRequest;
    }
    export interface Params$Resource$Projects$Locations$Repositories$Pullrequests$Pullrequestcomments$Unresolve extends StandardParameters {
        /**
         * Required. The pull request in which to resolve the pull request comments. Format: `projects/{project_number\}/locations/{location_id\}/repositories/{repository_id\}/pullRequests/{pull_request_id\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UnresolvePullRequestCommentsRequest;
    }
    export {};
}
