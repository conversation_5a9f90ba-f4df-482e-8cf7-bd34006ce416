-- Create stored procedure to calculate disc counts for Discraft OSL map records
-- This procedure counts discs for each OSL ID and updates the it_discraft_osl_map table

CREATE OR REPLACE FUNCTION calculate_discraft_osl_disc_counts(osl_ids INTEGER[])
RETURNS TABLE(
    osl_id INTEGER,
    in_stock SMALLINT,
    sold_last_90 SMALLINT,
    updated BOOLEAN
) AS $$
DECLARE
    current_osl_id INTEGER;
    in_stock_count SMALLINT;
    sold_last_90_count SMALLINT;
    ninety_days_ago TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Calculate the date 90 days ago
    ninety_days_ago := NOW() - INTERVAL '90 days';
    
    -- Process each OSL ID
    FOREACH current_osl_id IN ARRAY osl_ids
    LOOP
        -- Count in-stock discs (sold_date is null)
        SELECT COUNT(*)::SMALLINT INTO in_stock_count
        FROM t_discs
        WHERE vendor_osl_id = current_osl_id
          AND sold_date IS NULL;
        
        -- Count discs sold in last 90 days
        SELECT COUNT(*)::SMALLINT INTO sold_last_90_count
        FROM t_discs
        WHERE vendor_osl_id = current_osl_id
          AND sold_date >= ninety_days_ago;
        
        -- Update the OSL map record
        UPDATE it_discraft_osl_map
        SET 
            in_stock = in_stock_count,
            sold_last_90 = sold_last_90_count,
            updated_at = NOW()
        WHERE id = current_osl_id;
        
        -- Return the results
        osl_id := current_osl_id;
        in_stock := in_stock_count;
        sold_last_90 := sold_last_90_count;
        updated := TRUE;
        RETURN NEXT;
    END LOOP;
    
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to the appropriate role
-- GRANT EXECUTE ON FUNCTION calculate_discraft_osl_disc_counts(INTEGER[]) TO your_app_role;

-- Example usage:
-- SELECT * FROM calculate_discraft_osl_disc_counts(ARRAY[1840, 5957, 5958]);

-- Test the function with a small sample
-- SELECT * FROM calculate_discraft_osl_disc_counts(
--     ARRAY(SELECT id FROM it_discraft_osl_map LIMIT 5)
-- );
